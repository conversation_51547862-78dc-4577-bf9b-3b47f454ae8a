ปัญหา RUN ไม่ติดสำหรับ android studio มาจาก windows ปัญหานี้มักเกิดกับเครื่องที่ตั้งค่าภาษาเป็นไทยหรืออื่น ๆ ที่ไม่ใช่ en-US   //คอมส่วนตัว 
แก้ไขคือ เพิ่มค่าภาษา เปิดไฟล์ android/gradle.properties 
แล้วเพิ่มบรรทัดนี้
org.gradle.jvmargs=-Xmx1536m -Duser.language=en -Duser.country=US   ///แจ้งว่าใช้ภาษา US ที่ไม่ใช้ TH เพื่อRun 

ตรวจสอบและลบ resource ซ้ำ 
ไปที่่ android/app/builld.gradle เพิ่ม block เพื่อ exclude resource ซ้ำ

 packagingOptions {
        exclude 'META-INF/DEPENDENCIES'
        exclude 'META-INF/LICENSE'
        exclude 'META-INF/LICENSE.txt'
        exclude 'META-INF/NOTICE'
        exclude 'META-INF/NOTICE.txt'
    }



Project Structure

lib/
├── models/              # แบบจำลองข้อมูล (สินค้า, รายการสินค้าในรถเข็น)
├── providers/           # State management using Provider
├── pages/               # UI Pages ( Home, Product, Cart )
├── utils/               # Utility functions (Toast)
├── data/                # ข้อมูลจำลอง
└── main.dart            # Entry point

Clone the project
git clone <your-repo-url>
cd flutter_shopping_app

Install dependencies
flutter pub get

 Run the app
flutter run



Author : Chayutpong Boonsri
//
