class Product {
  final int id;
  final String name;
  final String imageUrl;
  final double price;

  Product({
    required this.id,
    required this.name,
    required this.imageUrl,
    required this.price,
  });

  factory Product.fromJson(Map<String, dynamic> json) => Product(
    // สร้าง Product จาก JSON
    id: json['id'],
    name: json['name'],
    imageUrl: json['image_url'],
    price: (json['price'] as num).toDouble(),
  );
}
/*
  Map<String, dynamic> toJson() => {
    // แปลง Product เป็น JSON
    'id': id,
    'name': name,
    'image_url': imageUrl,
    'price': price,
  };

  @override
  String toString() {
    return 'Product{id: $id, name: $name, imageUrl: $imageUrl, price: $price}';
  }
}
*/