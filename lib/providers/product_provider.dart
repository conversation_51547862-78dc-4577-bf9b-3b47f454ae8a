import 'package:flutter/material.dart';
import '../models/product.dart';

class ProductProvider with ChangeNotifier {
  List<Product> _allProducts = [];
  // รายการสินค้าทั้งหมด
  // ใช้ List<int> เพื่อเก็บ ID ของสินค้าที่ถูกบันทึก
  List<int> _savedProductIds = [];

  List<Product> get allProducts => _allProducts;
  List<Product> get savedProducts =>
      _allProducts.where((p) => _savedProductIds.contains(p.id)).toList();
  // สินค้าที่ถูกบันทึก

  void loadProducts(List<Product> products) {
    // โหลดสินค้าจากแหล่งข้อมูล
    // _allProducts.clear(); // ล้างรายการสินค้าทั้งหมดก่อน
    _allProducts = products; // กำหนดรายการสินค้าทั้งหมด
    notifyListeners(); // แจ้งว่ามีการเปลี่ยนแปลง
  }

  void toggleFavorite(int id) {
    // ฟังก์ชันสำหรับเพิ่มหรือลบสินค้าจากรายการโปรด
    if (_savedProductIds.contains(id)) {
      _savedProductIds.remove(id);
    } else {
      _savedProductIds.add(id);
    }
    notifyListeners();
  }

  bool isFavorite(int id) =>
      _savedProductIds.contains(id); // เช็คว่าสินค้าเป็นโปรดหรือไม่
}
