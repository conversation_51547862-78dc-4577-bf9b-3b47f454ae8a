import 'package:flutter/material.dart';
import '../models/product.dart';
import '../models/cart_item.dart';

class CartProvider with ChangeNotifier {
  final List<CartItem> _items = [];

  List<CartItem> get items => _items;

  void addToCart(Product product) {
    final index = _items.indexWhere((item) => item.product.id == product.id);
    if (index != -1) {
      _items[index].quantity++;
    } else {
      _items.add(CartItem(product: product));
    }
    notifyListeners();
  }

  void removeItem(Product product) {
    _items.removeWhere((item) => item.product.id == product.id);
    notifyListeners();
  }

  void increaseQuantity(Product product) {
    final item = _items.firstWhere((e) => e.product.id == product.id);
    item.quantity++;
    notifyListeners();
  }

  void decreaseQuantity(Product product) {
    final item = _items.firstWhere((e) => e.product.id == product.id);
    if (item.quantity > 1) {
      item.quantity--;
    } else {
      removeItem(product);
    }
    notifyListeners();
  }

  double get totalPrice => _items.fold(
    0.0,
    (sum, item) => sum + (item.product.price * item.quantity),
    // คำนวณราคารวมของสินค้าทั้งหมด
  );
}
