import 'package:flutter/material.dart';
import 'package:qr_flutter/qr_flutter.dart';

class CheckoutPage extends StatelessWidget {
  final double totalPrice;

  const CheckoutPage({super.key, required this.totalPrice});

  @override
  Widget build(BuildContext context) {
    // สร้างหน้าชำระเงิน
    final checkoutUrl =
        'https://payment.spw.challenge/checkout?price=${totalPrice.toStringAsFixed(2)}'; // สร้าง URL สำหรับการชำระเงิน
    // https://payment.spw.challenge/checkout?price=$

    return Scaffold(
      appBar: AppBar(
        title: const Text('Checkout'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            QrImageView(
              data: checkoutUrl,
              version: QrVersions.auto,
              size: 200.0,
            ),
            const SizedBox(height: 24),
            Text(
              'Total: \$${totalPrice.toStringAsFixed(2)}',
              style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
          ],
        ),
      ),
    );
  }
}
