import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

import '../models/product.dart';
import '../providers/product_provider.dart';
import '../utils/toast_util.dart';
import 'product_page.dart';

class SavedPage extends StatelessWidget {
  const SavedPage({super.key});

  @override
  Widget build(BuildContext context) {
    final savedProducts = context.watch<ProductProvider>().savedProducts;

    return Scaffold(
      appBar: AppBar(title: const Text('Saved')),
      body: savedProducts.isEmpty
          ? const Center(child: Text('ยังไม่มีรายการที่บันทึกไว้'))
          : Padding(
              padding: const EdgeInsets.all(8.0),
              child: GridView.builder(
                itemCount: savedProducts.length,
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  childAspectRatio: 0.75,
                  crossAxisSpacing: 8,
                  mainAxisSpacing: 8,
                ),
                itemBuilder: (context, index) {
                  final product = savedProducts[index];
                  return SavedProductCard(product: product);
                },
              ),
            ),
    );
  }
}

class SavedProductCard extends StatelessWidget {
  // สร้าง Card สำหรับแสดงสินค้าในหน้าบันทึก
  final Product product;
  const SavedProductCard({super.key, required this.product});

  @override
  Widget build(BuildContext context) {
    final provider = context.read<ProductProvider>();

    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (_) => ProductPage(product: product),
          ), // เมื่อกดที่ Card จะเปิดหน้ารายละเอียดสินค้า
        );
      },
      child: Card(
        // ใช้ Card เพื่อสร้างกรอบรอบๆ สินค้า
        elevation: 4,
        child: Stack(
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Expanded(
                  child: Image.network(product.imageUrl, fit: BoxFit.cover),
                ),
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Text(
                    product.name,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 8.0),
                  child: Text('\$${product.price.toStringAsFixed(2)}'),
                ),
              ],
            ),
            Positioned(
              right: 4,
              top: 4,
              child: IconButton(
                icon: const Icon(
                  FontAwesomeIcons.solidHeart,
                  color: Colors.red,
                  size: 20,
                ),
                onPressed: () {
                  provider.toggleFavorite(product.id);
                  showToast(context, 'ลบออกจากรายการโปรด');
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
