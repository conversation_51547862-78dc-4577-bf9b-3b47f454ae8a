import 'package:flutter/material.dart';

import 'home_page.dart';
import 'saved_page.dart';
import 'cart_page.dart';

class MainNavigationPage extends StatefulWidget {
  const MainNavigationPage({super.key});

  @override
  State<MainNavigationPage> createState() => _MainNavigationPageState();
}

class _MainNavigationPageState extends State<MainNavigationPage> {
  int _selectedIndex = 0;

  //เรียกใช้หน้า HomePage, SavedPage และ CartPage
  final List<Widget> _pages = const [HomePage(), SavedPage(), CartPage()];

  void _onItemTapped(int index) {
    setState(() {
      _selectedIndex = index;
    });
  }

  final List<BottomNavigationBarItem> _items = const [
    // รายการที่จะแสดงใน BottomNavigationBar
    BottomNavigationBarItem(icon: Icon(Icons.home), label: 'Home'),
    BottomNavigationBarItem(icon: Icon(Icons.favorite), label: 'Saved'),
    BottomNavigationBarItem(icon: Icon(Icons.shopping_cart), label: 'Cart'),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _pages[_selectedIndex],
      bottomNavigationBar: BottomNavigationBar(
        items: _items,
        currentIndex: _selectedIndex,
        selectedItemColor: Colors.blue,
        onTap: _onItemTapped,
      ),
    );
  }
}
