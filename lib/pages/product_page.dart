import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

import '../models/product.dart';
import '../providers/product_provider.dart';
import '../providers/cart_provider.dart';
import '../utils/toast_util.dart';

class ProductPage extends StatelessWidget {
  // ใช้ StatelessWidget เพราะไม่ต้องการจัดการสถานะภายในหน้า
  final Product product;
  // โมเดลสินค้าที่จะถูกแสดงในหน้ารายละเอียดสินค้า
  // สร้างคอนสตรัคเตอร์เพื่อรับโมเดลสินค้า

  const ProductPage({super.key, required this.product});
  // คอนสตรัคเตอร์รับโมเดลสินค้า

  @override
  // เมธอด build จะถูกเรียกเมื่อสร้าง widget
  // เมธอดนี้จะสร้าง UI ของหน้ารายละเอียดสินค้า
  Widget build(BuildContext context) {
    final isFavorite = context.watch<ProductProvider>().isFavorite(product.id);
    final productProvider = context.read<ProductProvider>();
    final cartProvider = context.read<CartProvider>();
    final screenHeight = MediaQuery.of(context).size.height;

    return Scaffold(
      backgroundColor: Colors.grey[100],
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        foregroundColor: Colors.black87,
      ),
      body: Column(
        children: [
          const SizedBox(height: 20), // เว้นระยะห่างด้านบน
          // แสดงรูปภาพสินค้า
          Center(
            child: Stack(
              // ใช้ Stack เพื่อวางรูปภาพและปุ่มไอคอนทับกัน
              alignment: Alignment.topRight, // วางปุ่มไอคอนที่มุม
              children: [
                ClipRRect(
                  // ใช้ ClipRRect เพื่อทำให้รูปภาพมีมุมโค้งมน
                  borderRadius: const BorderRadius.only(
                    // กำหนดมุมโค้งมนให้กับรูปภาพ
                    topLeft: Radius.circular(40), // มุมซ้ายบน
                    topRight: Radius.circular(40), // มุมขวาบน
                    bottomLeft: Radius.circular(40), // มุมซ้ายล่าง
                    bottomRight: Radius.circular(40), // มุมขวาล่าง
                  ),

                  child: Image.network(
                    product.imageUrl, // ดึง URL ของรูปภาพจากโมเดลสินค้า
                    width:
                        MediaQuery.of(context).size.width *
                        0.8, // กำหนดความกว้างของรูปภาพ
                    height: screenHeight * 0.35, // กำหนดความสูงของรูปภาพ
                    fit: BoxFit.cover, // ปรับขนาดรูปภาพให้พอดีกับกรอบ
                  ),
                ),

                Positioned(
                  top: 16, // วางปุ่มไอคอนที่มุมขวาบน
                  right: 16, // วางปุ่มไอคอนที่มุมขวาบน
                  child: IconButton(
                    icon: Icon(
                      isFavorite
                          ? FontAwesomeIcons.solidHeart
                          : FontAwesomeIcons.heart,
                      color: isFavorite ? Colors.redAccent : Colors.white,
                      // เปลี่ยนสีไอคอนตามสถานะการติดตาม
                      // ถ้าเป็นรายการโปรดจะเป็นสีแดง ถ้าไม่ใช่จะเป็นสีขาว
                      // ใช้ FontAwesomeIcons เพื่อแสดงไอคอนหัวใจ
                      size: 30, // กำหนดขนาดของไอคอน
                    ),

                    onPressed: () {
                      productProvider.toggleFavorite(product.id);
                      showToast(
                        context,
                        isFavorite ? 'เลิกติดตามแล้ว' : 'ติดตามแล้ว',
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  product.name,
                  style: const TextStyle(
                    fontSize: 22,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  '\$${product.price.toStringAsFixed(2)}',
                  style: const TextStyle(
                    fontSize: 18,
                    color: Colors.blueAccent,
                  ),
                ),
                const SizedBox(
                  height: 20,
                ), // เว้นระยะห่างก่อนแสดงรายละเอียดสินค้า
                const Text(
                  'ข้อมูลสินค้า',
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                ),
                const SizedBox(height: 6),
                // แสดงรายละเอียดสินค้า
                Text(
                  '"${product.name}" เป็นสินค้าคุณภาพเยี่ยมที่ออกแบบมาเพื่อคุณโดยเฉพาะ เพิ่มสินค้าลงในตะกร้าแล้วเพลิดเพลินได้เลย .', // product.name ดึงค่ามาแสดง เปลี่ยนตามสินค้าที่เลือก
                  style: const TextStyle(color: Colors.black87),
                ),
              ],
            ),
          ),
          const Spacer(),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 16),
            child: SizedBox(
              width: double.infinity,
              height: 52,
              child: ElevatedButton.icon(
                icon: const Icon(Icons.add_shopping_cart),
                label: const Text('เพิ่มลงตะกร้า'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blueAccent,
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  textStyle: const TextStyle(fontSize: 16),
                ),
                onPressed: () {
                  cartProvider.addToCart(product);
                  showToast(context, 'เพิ่มลงตะกร้าแล้ว');
                },
              ),
            ),
          ),
        ],
      ),
    );
  }
}
