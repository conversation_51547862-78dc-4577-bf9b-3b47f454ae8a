import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

import '../providers/product_provider.dart';
import '../models/product.dart';
import '../utils/toast_util.dart'; // เราจะสร้างต่อไป

import 'product_page.dart';

import 'saved_page.dart';

class HomePage extends StatelessWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    final products = context
        .watch<ProductProvider>()
        .allProducts; // ดึงรายการสินค้าทั้งหมดจาก ProductProvider

    return Scaffold(
      // appBar: AppBar(title: const Text('Home')),
      appBar: AppBar(
        // ใช้ AppBar เพื่อแสดงหัวเรื่อง
        title: const Text('FOR YOU'),
        actions: [
          IconButton(
            icon: const Icon(Icons.favorite),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(builder: (_) => const SavedPage()),
              );
            },
          ),
        ],
      ),

      body: Padding(
        // ใช้ Padding เพื่อเว้นระยะห่างรอบๆ GridView
        padding: const EdgeInsets.all(8.0),
        child: GridView.builder(
          itemCount: products.length,
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            childAspectRatio: 0.75,
            crossAxisSpacing: 8,
            mainAxisSpacing: 8,
          ),
          itemBuilder: (context, index) {
            final product = products[index];
            return ProductCard(product: product);
          },
        ),
      ),
    );
  }
}

class ProductCard extends StatelessWidget {
  // สร้าง Card สำหรับแสดงสินค้าในหน้า HomePage
  // สร้างคลาส ProductCard ที่รับโมเดล Product
  final Product product;
  const ProductCard({super.key, required this.product});

  @override
  Widget build(BuildContext context) {
    final provider = context.read<ProductProvider>();
    final isFavorite = context.watch<ProductProvider>().isFavorite(product.id);

    return GestureDetector(
      /*  onTap: () {
        // TODO: ไปหน้า ProductPage
      },
*/
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(builder: (_) => ProductPage(product: product)),
        );
      },

      child: Card(
        elevation: 4,
        child: Stack(
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Expanded(
                  child: Image.network(product.imageUrl, fit: BoxFit.cover),
                ),
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Text(
                    product.name,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 8.0),
                  child: Text('\$${product.price.toStringAsFixed(2)}'),
                ),
              ],
            ),
            Positioned(
              right: 4,
              top: 4,
              child: IconButton(
                icon: Icon(
                  isFavorite
                      ? FontAwesomeIcons.solidHeart
                      : FontAwesomeIcons.heart,
                  color: isFavorite ? Colors.red : Colors.grey,
                  size: 20,
                ),
                onPressed: () {
                  provider.toggleFavorite(product.id);
                  showToast(
                    context,
                    isFavorite ? 'ลบออกจากรายการโปรด' : 'เพิ่มลงในรายการโปรด',
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
