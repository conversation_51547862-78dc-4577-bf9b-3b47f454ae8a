import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'providers/product_provider.dart';
import 'providers/cart_provider.dart';
import 'data/mock_products.dart';
import 'pages/home_page.dart';
import 'pages/main_navigation_pages.dart';

void main() {
  runApp(
    MultiProvider(
      providers: [
        ChangeNotifierProvider(
          // ใช้ ChangeNotifierProvider เพื่อจัดการสถานะของ ProductProvider
          create: (_) =>
              ProductProvider()..loadProducts(mockProducts), // โหลด mock data
        ),
        ChangeNotifierProvider(
          create: (_) => CartProvider(),
        ), // เพิ่ม CartProvider
      ],
      child: const MyApp(), // สร้าง MyApp ที่เป็น StatelessWidget
    ),
  );
}

class MyApp extends StatelessWidget {
  const MyApp({super.key}); // สร้าง StatelessWidget สำหรับแอปหลัก

  @override
  Widget build(BuildContext context) {
    // สร้าง MaterialApp ที่เป็นแอปหลัก
    return MaterialApp(
      title: 'DEMO Shopping App', // ชื่อแอป
      theme: ThemeData(primarySwatch: Colors.blue), // กำหนดธีมหลักของแอป
      home: const MainNavigationPage(), // เปลี่ยนเป็น MainNavigationPage
    );
  }
}
