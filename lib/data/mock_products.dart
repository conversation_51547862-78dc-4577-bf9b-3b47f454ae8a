import '../models/product.dart';

final mockProducts = [
  Product(
    id: 1,
    name: '<PERSON><PERSON> (New)',
    imageUrl:
        'https://images.unsplash.com/photo-1608231387042-66d1773070a5?fit=crop&w=300&q=80',
    price: 250.00,
  ),

  Product(
    id: 2,
    name: 'iPhone 12 Pro Black Edition',
    imageUrl:
        'https://images.unsplash.com/photo-1573148195900-7845dcb9b127?fit=crop&w=300&q=80',
    price: 1200.00,
  ),
  Product(
    id: 3,
    name: 'Nintendo Switch 2021',
    imageUrl:
        'https://images.unsplash.com/photo-1578303512597-81e6cc155b3e?fit=crop&w=300&q=80',
    price: 599.00,
  ),

  Product(
    id: 4,
    name: 'Black + Decker',
    imageUrl:
        'https://images.unsplash.com/photo-1574269909862-7e1d70bb8078?fit=crop&w=300&q=80',
    price: 149.00,
  ),

  Product(
    id: 5,
    name: 'White Neat Mug',
    imageUrl:
        'https://images.unsplash.com/photo-1514228742587-6b1558fcca3d?fit=crop&w=300&q=80',
    price: 35.00,
  ),
  Product(
    id: 6,
    name: 'SMEG Oven - Winter Collection',
    imageUrl:
        'https://images.unsplash.com/photo-1586208958839-06c17cacdf08?fit=crop&w=300&q=80',
    price: 8299.00,
  ),

  Product(
    id: 7,
    name: 'Black Table Fan with Pink Moody Cat',
    imageUrl:
        'https://images.unsplash.com/photo-1618941716939-553df3c6c278?fit=crop&w=300&q=80',
    price: 79.00,
  ),
];
